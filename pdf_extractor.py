#!/usr/bin/env python3
"""
PDF Text Extractor for Psychometrist Portal
Step 1: Extract text from psychological assessment PDFs
"""

import PyPDF2
import os
import pandas as pd
from pathlib import Path

def extract_text_from_pdf(pdf_path):
    """
    Extract text from a PDF file
    
    Args:
        pdf_path (str): Path to the PDF file
        
    Returns:
        str: Extracted text from the PDF
    """
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            # Extract text from all pages
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                text += f"\n--- Page {page_num + 1} ---\n"
                text += page_text
                
        return text
    except Exception as e:
        return f"ERROR: Could not read PDF - {str(e)}"

def test_single_pdf():
    """Test extraction on a single PDF file"""
    print("=== Testing Single PDF Extraction ===")
    
    # Test file path
    pdf_path = "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/2_ViGo_WISC.pdf"
    
    print(f"Testing file: {pdf_path}")
    
    # Check if file exists
    if not os.path.exists(pdf_path):
        print(f"ERROR: File not found: {pdf_path}")
        return
    
    # Extract text
    text = extract_text_from_pdf(pdf_path)
    
    # Display results
    print(f"Text length: {len(text)} characters")
    print(f"First 500 characters:")
    print("-" * 50)
    print(text[:500])
    print("-" * 50)
    
    return text

def test_multiple_pdfs():
    """Test extraction on multiple PDF files"""
    print("\n=== Testing Multiple PDF Extraction ===")
    
    # List of test files
    test_files = [
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/2_ViGo_WISC.pdf",
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/3_ViGo_WIAT_.pdf",
        "OWL Downloads/1161_ADi_2019-11-21_2025-05-02/documents/3_AlDi_WAIS_American_report.pdf",
        "OWL Downloads/1166_EBa_2019-12-01_2025-05-02/documents/8_EnBa_WIAT_and_WISC.pdf",
        "OWL Downloads/1167_ACr_2019-12-07_2025-05-02/documents/35_AdCr_DD-WISC_Report_with_Cancellation_and_Skills_Analysis.pdf"
    ]
    
    results = []
    
    for file_path in test_files:
        print(f"\n--- Testing: {os.path.basename(file_path)} ---")
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            continue
            
        text = extract_text_from_pdf(file_path)
        
        if text.startswith("ERROR"):
            print(f"❌ {text}")
        else:
            print(f"✅ Success! Text length: {len(text)} characters")
            print(f"First 200 characters: {text[:200]}")
            
        results.append({
            'file': os.path.basename(file_path),
            'path': file_path,
            'success': not text.startswith("ERROR"),
            'text_length': len(text) if not text.startswith("ERROR") else 0,
            'preview': text[:200] if not text.startswith("ERROR") else text
        })
    
    return results

def scan_all_pdfs():
    """Scan all PDF files in the OWL Downloads directory"""
    print("\n=== Scanning All PDF Files ===")
    
    owl_dir = "OWL Downloads"
    pdf_files = []
    
    # Find all PDF files
    for root, dirs, files in os.walk(owl_dir):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    
    print(f"Found {len(pdf_files)} PDF files")
    
    # Test first 10 files
    test_count = min(10, len(pdf_files))
    print(f"Testing first {test_count} files...")
    
    results = []
    for i, pdf_path in enumerate(pdf_files[:test_count]):
        print(f"\n{i+1}/{test_count}: {os.path.basename(pdf_path)}")
        
        text = extract_text_from_pdf(pdf_path)
        success = not text.startswith("ERROR")
        
        if success:
            print(f"✅ Success! {len(text)} characters")
        else:
            print(f"❌ Failed: {text}")
            
        results.append({
            'file': os.path.basename(pdf_path),
            'path': pdf_path,
            'success': success,
            'text_length': len(text) if success else 0
        })
    
    return results

if __name__ == "__main__":
    print("🚀 Starting PDF Text Extraction Tests")
    print("=" * 60)
    
    # Test 1: Single PDF
    single_result = test_single_pdf()
    
    # Test 2: Multiple specific PDFs
    multiple_results = test_multiple_pdfs()
    
    # Test 3: Scan all PDFs (first 10)
    scan_results = scan_all_pdfs()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    if multiple_results:
        successful = sum(1 for r in multiple_results if r['success'])
        total = len(multiple_results)
        print(f"Multiple PDF Test: {successful}/{total} files successfully processed")
    
    if scan_results:
        successful = sum(1 for r in scan_results if r['success'])
        total = len(scan_results)
        print(f"PDF Scan Test: {successful}/{total} files successfully processed")
    
    print("\n✅ PDF extraction testing complete!")
    print("Next step: Analyze the extracted text to identify document types")
