#!/usr/bin/env python3
"""
Document Classifier for Psychological Assessments
Step 3: Automatically identify document types
"""

import os
import re
from pdf_extractor import extract_text_from_pdf

class DocumentClassifier:
    """Simple rule-based classifier for psychological assessment documents"""
    
    def __init__(self):
        # Define keywords for each document type
        self.classification_rules = {
            'WISC': {
                'keywords': ['WISC', 'Wechsler Intelligence Scale for Children'],
                'required_terms': ['Full Scale IQ', 'Verbal Comprehension', 'Working Memory'],
                'age_range': 'children'
            },
            'WAIS': {
                'keywords': ['WAIS', 'Wechsler Adult Intelligence Scale'],
                'required_terms': ['Full Scale IQ', 'Verbal Comprehension', 'Working Memory'],
                'age_range': 'adult'
            },
            'WIAT': {
                'keywords': ['WIAT', 'Wechsler Individual Achievement Test'],
                'required_terms': ['reading', 'math', 'achievement'],
                'age_range': 'any'
            },
            'BASC': {
                'keywords': ['BASC', 'Behavior Assessment System for Children'],
                'required_terms': ['behavior', 'adaptive', 'clinical'],
                'age_range': 'children'
            },
            'Conners': {
                'keywords': ['Conners', 'ADHD'],
                'required_terms': ['attention', 'hyperactivity'],
                'age_range': 'any'
            },
            'NEPSY': {
                'keywords': ['NEPSY', 'neuropsychological'],
                'required_terms': ['attention', 'executive', 'memory'],
                'age_range': 'children'
            },
            'Consent_Form': {
                'keywords': ['consent', 'privacy policy', 'assessment agreement'],
                'required_terms': ['agree', 'understand', 'signature'],
                'age_range': 'any'
            },
            'Report': {
                'keywords': ['psychological report', 'assessment report', 'evaluation'],
                'required_terms': ['background', 'recommendations', 'summary'],
                'age_range': 'any'
            }
        }
    
    def classify_document(self, text):
        """
        Classify a document based on its text content
        
        Args:
            text (str): Extracted text from document
            
        Returns:
            dict: Classification results
        """
        text_lower = text.lower()
        
        # Score each document type
        scores = {}
        
        for doc_type, rules in self.classification_rules.items():
            score = 0
            
            # Check for primary keywords
            for keyword in rules['keywords']:
                if keyword.lower() in text_lower:
                    score += 10  # High weight for primary keywords
            
            # Check for required terms
            for term in rules['required_terms']:
                if term.lower() in text_lower:
                    score += 3  # Medium weight for required terms
            
            scores[doc_type] = score
        
        # Find the best match
        if max(scores.values()) > 0:
            best_match = max(scores, key=scores.get)
            confidence = scores[best_match]
            
            return {
                'classification': best_match,
                'confidence': confidence,
                'all_scores': scores,
                'text_length': len(text)
            }
        else:
            return {
                'classification': 'Unknown',
                'confidence': 0,
                'all_scores': scores,
                'text_length': len(text)
            }
    
    def classify_file(self, file_path):
        """Classify a PDF file"""
        if not os.path.exists(file_path):
            return {'error': f'File not found: {file_path}'}
        
        # Extract text
        text = extract_text_from_pdf(file_path)
        
        if text.startswith('ERROR'):
            return {'error': text}
        
        # Classify
        result = self.classify_document(text)
        result['file_path'] = file_path
        result['file_name'] = os.path.basename(file_path)
        
        return result

def test_classifier():
    """Test the classifier on known documents"""
    print("🤖 Testing Document Classifier")
    print("=" * 60)
    
    classifier = DocumentClassifier()
    
    # Test files with known types
    test_files = [
        {
            'path': 'OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/2_ViGo_WISC.pdf',
            'expected': 'WISC'
        },
        {
            'path': 'OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/3_ViGo_WIAT_.pdf',
            'expected': 'WIAT'
        },
        {
            'path': 'OWL Downloads/1161_ADi_2019-11-21_2025-05-02/documents/3_AlDi_WAIS_American_report.pdf',
            'expected': 'WAIS'
        },
        {
            'path': 'OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/11_ViGo_BASC_Parent.pdf',
            'expected': 'BASC'
        },
        {
            'path': 'OWL Downloads/1148_VGo_2019-11-02_2025-05-02/forms/VGo_Dr._Drahovzal_-_Consent_for_Assessment_b_-_Information_for_Clients_2023-11-17.pdf',
            'expected': 'Consent_Form'
        }
    ]
    
    correct_predictions = 0
    total_predictions = 0
    
    for test_file in test_files:
        print(f"\n--- Testing: {os.path.basename(test_file['path'])} ---")
        
        result = classifier.classify_file(test_file['path'])
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            continue
        
        predicted = result['classification']
        expected = test_file['expected']
        confidence = result['confidence']
        
        if predicted == expected:
            print(f"✅ CORRECT! Predicted: {predicted} (confidence: {confidence})")
            correct_predictions += 1
        else:
            print(f"❌ WRONG! Predicted: {predicted}, Expected: {expected} (confidence: {confidence})")
            print(f"   All scores: {result['all_scores']}")
        
        total_predictions += 1
    
    # Calculate accuracy
    if total_predictions > 0:
        accuracy = (correct_predictions / total_predictions) * 100
        print(f"\n📊 ACCURACY: {correct_predictions}/{total_predictions} = {accuracy:.1f}%")
    
    return classifier

def classify_random_documents(classifier, num_files=10):
    """Classify random documents to see what we find"""
    print(f"\n🎲 Classifying {num_files} Random Documents")
    print("=" * 60)
    
    # Find all PDF files
    pdf_files = []
    for root, dirs, files in os.walk("OWL Downloads"):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    
    # Test random selection
    import random
    random.seed(42)  # For reproducible results
    test_files = random.sample(pdf_files, min(num_files, len(pdf_files)))
    
    classification_counts = {}
    
    for i, file_path in enumerate(test_files):
        print(f"\n{i+1}/{num_files}: {os.path.basename(file_path)}")
        
        result = classifier.classify_file(file_path)
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            continue
        
        classification = result['classification']
        confidence = result['confidence']
        
        print(f"📋 Classification: {classification} (confidence: {confidence})")
        
        # Count classifications
        if classification not in classification_counts:
            classification_counts[classification] = 0
        classification_counts[classification] += 1
    
    # Show summary
    print(f"\n📊 CLASSIFICATION SUMMARY")
    print("-" * 30)
    for doc_type, count in sorted(classification_counts.items()):
        print(f"{doc_type}: {count} documents")

if __name__ == "__main__":
    print("🚀 Starting Document Classification")
    print("=" * 60)
    
    # Test the classifier
    classifier = test_classifier()
    
    # Classify random documents
    classify_random_documents(classifier, num_files=15)
    
    print("\n" + "=" * 60)
    print("🎯 CLASSIFICATION COMPLETE!")
    print("=" * 60)
    print("✅ Built a working document classifier")
    print("✅ Can identify WISC, WIAT, WAIS, BASC, and other documents")
    print("✅ Ready for next step: Extract specific information from documents")
    print("\nNext: Build information extraction for test scores and patient data!")
