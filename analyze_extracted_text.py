#!/usr/bin/env python3
"""
Analyze Extracted Text from PDFs
Step 2: Understand what's in the extracted text
"""

import PyPDF2
import os
import re
from pdf_extractor import extract_text_from_pdf

def analyze_wisc_report():
    """Analyze a WISC report to understand its structure"""
    print("=== Analyzing WISC Report Structure ===")
    
    pdf_path = "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/2_ViGo_WISC.pdf"
    text = extract_text_from_pdf(pdf_path)
    
    print(f"File: {os.path.basename(pdf_path)}")
    print(f"Total text length: {len(text)} characters")
    print(f"Number of pages: {text.count('--- Page')}")
    
    # Look for key sections
    print("\n=== Key Sections Found ===")
    
    # Common WISC sections
    sections_to_find = [
        "WISC",
        "Wechsler Intelligence Scale",
        "Full Scale IQ",
        "Verbal Comprehension",
        "Perceptual Reasoning",
        "Working Memory",
        "Processing Speed",
        "Composite Score",
        "Percentile",
        "Standard Score"
    ]
    
    for section in sections_to_find:
        if section.lower() in text.lower():
            print(f"✅ Found: {section}")
        else:
            print(f"❌ Missing: {section}")
    
    # Extract numbers that look like scores
    print("\n=== Potential Scores Found ===")
    score_pattern = r'\b\d{2,3}\b'  # 2-3 digit numbers (typical for IQ scores)
    scores = re.findall(score_pattern, text)
    unique_scores = list(set(scores))
    unique_scores.sort()
    print(f"Found potential scores: {unique_scores[:20]}")  # Show first 20
    
    # Show first 1000 characters for manual inspection
    print("\n=== First 1000 Characters ===")
    print(text[:1000])
    
    return text

def compare_document_types():
    """Compare different types of documents to identify patterns"""
    print("\n=== Comparing Different Document Types ===")
    
    test_files = {
        "WISC": "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/2_ViGo_WISC.pdf",
        "WIAT": "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/3_ViGo_WIAT_.pdf",
        "WAIS": "OWL Downloads/1161_ADi_2019-11-21_2025-05-02/documents/3_AlDi_WAIS_American_report.pdf"
    }
    
    for doc_type, file_path in test_files.items():
        print(f"\n--- {doc_type} Document ---")
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            continue
            
        text = extract_text_from_pdf(file_path)
        
        # Look for identifying keywords
        keywords = {
            "WISC": ["WISC", "Wechsler Intelligence Scale for Children"],
            "WIAT": ["WIAT", "Wechsler Individual Achievement Test"],
            "WAIS": ["WAIS", "Wechsler Adult Intelligence Scale"]
        }
        
        print(f"Text length: {len(text)} characters")
        
        # Check for identifying keywords
        for test_type, test_keywords in keywords.items():
            found = any(keyword.lower() in text.lower() for keyword in test_keywords)
            if found:
                print(f"✅ Identified as: {test_type}")
                break
        else:
            print("❓ Could not identify document type")
        
        # Show first 300 characters
        print(f"Preview: {text[:300]}...")

def find_assessment_keywords():
    """Find common keywords across all assessment documents"""
    print("\n=== Finding Common Assessment Keywords ===")
    
    # Test multiple assessment files
    assessment_files = [
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/2_ViGo_WISC.pdf",
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/3_ViGo_WIAT_.pdf",
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/11_ViGo_BASC_Parent.pdf",
        "OWL Downloads/1161_ADi_2019-11-21_2025-05-02/documents/3_AlDi_WAIS_American_report.pdf"
    ]
    
    all_text = ""
    
    for file_path in assessment_files:
        if os.path.exists(file_path):
            text = extract_text_from_pdf(file_path)
            all_text += text.lower() + " "
            print(f"✅ Processed: {os.path.basename(file_path)}")
        else:
            print(f"❌ Missing: {os.path.basename(file_path)}")
    
    # Find common psychological assessment terms
    assessment_terms = [
        "standard score", "percentile", "composite", "index", "scaled score",
        "verbal", "performance", "cognitive", "achievement", "intelligence",
        "iq", "full scale", "working memory", "processing speed",
        "reading", "writing", "math", "comprehension", "reasoning"
    ]
    
    print("\n=== Assessment Terms Found ===")
    for term in assessment_terms:
        count = all_text.count(term)
        if count > 0:
            print(f"✅ '{term}': found {count} times")
        else:
            print(f"❌ '{term}': not found")

if __name__ == "__main__":
    print("🔍 Analyzing Extracted Text from PDFs")
    print("=" * 60)
    
    # Step 1: Analyze WISC report structure
    wisc_text = analyze_wisc_report()
    
    # Step 2: Compare different document types
    compare_document_types()
    
    # Step 3: Find common assessment keywords
    find_assessment_keywords()
    
    print("\n" + "=" * 60)
    print("📊 ANALYSIS COMPLETE")
    print("=" * 60)
    print("✅ We can successfully extract text from PDFs")
    print("✅ We can identify different document types")
    print("✅ We found common assessment terminology")
    print("\nNext step: Build a document classifier!")
