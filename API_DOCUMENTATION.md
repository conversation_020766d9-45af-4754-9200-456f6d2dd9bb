# Psychometrist Portal API Documentation

## Overview
The Psychometrist Portal API provides endpoints for managing patients, uploading psychological assessment documents, processing them with AI, and generating comprehensive reports.

**Base URL**: `http://localhost:5000/api`

## Authentication
Currently no authentication required (demo mode). In production, add JWT tokens or API keys.

## Endpoints

### 1. Health Check
Check if the API server is running.

**Endpoint**: `GET /api/health`

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2025-05-25T16:47:47.123456",
  "version": "1.0.0"
}
```

**cURL Example**:
```bash
curl -X GET http://localhost:5000/api/health
```

---

### 2. Patient Management

#### Create Patient
Create a new patient record.

**Endpoint**: `POST /api/patients`

**Request Body**:
```json
{
  "name": "<PERSON> Good",
  "age": "9:1",
  "grade": "3",
  "date_of_birth": "2015/01/25"
}
```

**Response**:
```json
{
  "message": "Patient created successfully",
  "patient": {
    "id": "afdc91c6-81d7-48db-9155-676a4e05cb63",
    "name": "Viktor Good",
    "age": "9:1",
    "grade": "3",
    "date_of_birth": "2015/01/25",
    "created_at": "2025-05-25T16:47:47.123456",
    "assessments": []
  }
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:5000/api/patients \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Viktor Good",
    "age": "9:1", 
    "grade": "3",
    "date_of_birth": "2015/01/25"
  }'
```

#### List All Patients
Get all patients in the system.

**Endpoint**: `GET /api/patients`

**Response**:
```json
{
  "patients": [
    {
      "id": "afdc91c6-81d7-48db-9155-676a4e05cb63",
      "name": "Viktor Good",
      "age": "9:1",
      "grade": "3",
      "assessments": ["assessment-id-1", "assessment-id-2"]
    }
  ],
  "count": 1
}
```

#### Get Patient Details
Get detailed information about a specific patient including their assessments.

**Endpoint**: `GET /api/patients/{patient_id}`

**Response**:
```json
{
  "patient": {
    "id": "afdc91c6-81d7-48db-9155-676a4e05cb63",
    "name": "Viktor Good",
    "age": "9:1",
    "grade": "3",
    "assessments": ["assessment-id-1", "assessment-id-2"]
  },
  "assessments": [
    {
      "id": "assessment-id-1",
      "document_type": "WISC",
      "classification_confidence": 29,
      "status": "processed",
      "uploaded_at": "2025-05-25T16:47:47.123456"
    }
  ]
}
```

#### Update Patient
Update patient information.

**Endpoint**: `PUT /api/patients/{patient_id}`

**Request Body**:
```json
{
  "name": "Viktor Good Updated",
  "age": "9:2",
  "grade": "3"
}
```

#### Delete Patient
Delete a patient and all associated data.

**Endpoint**: `DELETE /api/patients/{patient_id}`

---

### 3. Document Upload & Processing

#### Upload Assessment Documents
Upload one or more PDF assessment documents for processing.

**Endpoint**: `POST /api/upload`

**Request**: Multipart form data
- `files`: One or more PDF files
- `patient_id`: ID of the patient these assessments belong to

**Response**:
```json
{
  "message": "Processed 2 files",
  "results": [
    {
      "filename": "2_ViGo_WISC.pdf",
      "status": "success",
      "assessment_id": "assessment-id-1",
      "document_type": "WISC",
      "confidence": 29,
      "fields_extracted": 16
    },
    {
      "filename": "3_ViGo_WIAT_.pdf", 
      "status": "success",
      "assessment_id": "assessment-id-2",
      "document_type": "WIAT",
      "confidence": 29,
      "fields_extracted": 29
    }
  ]
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:5000/api/upload \
  -F "files=@path/to/wisc_report.pdf" \
  -F "files=@path/to/wiat_report.pdf" \
  -F "patient_id=afdc91c6-81d7-48db-9155-676a4e05cb63"
```

#### Classify Document Only
Classify a document without full processing (quick preview).

**Endpoint**: `POST /api/classify`

**Request**: Multipart form data
- `file`: Single PDF file

**Response**:
```json
{
  "classification": "WISC",
  "confidence": 29,
  "all_scores": {
    "WISC": 29,
    "WIAT": 15,
    "BASC": 8
  },
  "text_length": 9814
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:5000/api/classify \
  -F "file=@path/to/assessment.pdf"
```

---

### 4. Assessment Data

#### Get Assessment Details
Get detailed extracted data from a specific assessment.

**Endpoint**: `GET /api/assessments/{assessment_id}`

**Response**:
```json
{
  "assessment": {
    "id": "assessment-id-1",
    "patient_id": "patient-id",
    "filename": "2_ViGo_WISC.pdf",
    "document_type": "WISC",
    "classification_confidence": 29,
    "extracted_data": {
      "patient_info": {
        "name": "Viktor Good",
        "age": "9:1",
        "test_date": "2024/03/06"
      },
      "composite_scores": {
        "full_scale_iq": 95,
        "verbal_comprehension": 100,
        "processing_speed": 80
      },
      "percentiles": {
        "fsiq_percentile": 37,
        "psi_percentile": 9
      }
    },
    "uploaded_at": "2025-05-25T16:47:47.123456",
    "status": "processed"
  }
}
```

---

### 5. Report Generation

#### Generate Comprehensive Report
Generate a comprehensive psychological report combining multiple assessments.

**Endpoint**: `POST /api/reports/generate`

**Request Body**:
```json
{
  "patient_id": "afdc91c6-81d7-48db-9155-676a4e05cb63",
  "assessment_ids": ["assessment-id-1", "assessment-id-2"]
}
```

**Response**:
```json
{
  "message": "Report generated successfully",
  "report_id": "f68c782c-4d97-444c-9687-d33b006150cb",
  "report": {
    "id": "f68c782c-4d97-444c-9687-d33b006150cb",
    "patient_id": "afdc91c6-81d7-48db-9155-676a4e05cb63",
    "assessment_ids": ["assessment-id-1", "assessment-id-2"],
    "assessment_types": ["WISC", "WIAT"],
    "filename": "report_patient-id_20250525_164747.txt",
    "generated_at": "2025-05-25T16:47:47.123456",
    "status": "completed"
  },
  "preview": "================================================================================\nCOMPREHENSIVE PSYCHOLOGICAL ASSESSMENT REPORT\n..."
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:5000/api/reports/generate \
  -H "Content-Type: application/json" \
  -d '{
    "patient_id": "afdc91c6-81d7-48db-9155-676a4e05cb63",
    "assessment_ids": ["assessment-id-1", "assessment-id-2"]
  }'
```

#### Get Report Details
Get report metadata or download the report file.

**Endpoint**: `GET /api/reports/{report_id}`

**Query Parameters**:
- `download=true`: Download the report file instead of metadata

**Response (metadata)**:
```json
{
  "report": {
    "id": "f68c782c-4d97-444c-9687-d33b006150cb",
    "patient_id": "afdc91c6-81d7-48db-9155-676a4e05cb63",
    "assessment_types": ["WISC", "WIAT"],
    "filename": "report_patient-id_20250525_164747.txt",
    "generated_at": "2025-05-25T16:47:47.123456",
    "status": "completed"
  }
}
```

**Download Report**:
```bash
curl -X GET "http://localhost:5000/api/reports/f68c782c-4d97-444c-9687-d33b006150cb?download=true" \
  --output report.txt
```

#### List Reports
Get all reports, optionally filtered by patient.

**Endpoint**: `GET /api/reports`

**Query Parameters**:
- `patient_id`: Filter reports for specific patient

**Response**:
```json
{
  "reports": [
    {
      "id": "f68c782c-4d97-444c-9687-d33b006150cb",
      "patient_id": "afdc91c6-81d7-48db-9155-676a4e05cb63",
      "assessment_types": ["WISC", "WIAT"],
      "generated_at": "2025-05-25T16:47:47.123456"
    }
  ],
  "count": 1
}
```

---

### 6. System Statistics

#### Get System Statistics
Get overall system usage statistics and recent activity.

**Endpoint**: `GET /api/stats`

**Response**:
```json
{
  "statistics": {
    "total_patients": 1,
    "total_assessments": 2,
    "total_reports": 1,
    "assessment_types": {
      "WISC": 1,
      "WIAT": 1
    }
  },
  "recent_activity": {
    "assessments": [
      {
        "id": "assessment-id-1",
        "document_type": "WISC",
        "uploaded_at": "2025-05-25T16:47:47.123456"
      }
    ],
    "reports": [
      {
        "id": "report-id-1",
        "generated_at": "2025-05-25T16:47:47.123456"
      }
    ]
  }
}
```

---

## Supported Assessment Types

The API can process and extract information from:

1. **WISC** (Wechsler Intelligence Scale for Children)
   - Cognitive assessment
   - IQ scores and indices
   - Percentile rankings

2. **WIAT** (Wechsler Individual Achievement Test)
   - Academic achievement assessment
   - Reading, writing, math scores
   - Composite and subtest scores

3. **WAIS** (Wechsler Adult Intelligence Scale)
   - Adult cognitive assessment
   - Similar to WISC but for adults

4. **BASC** (Behavior Assessment System for Children)
   - Behavioral assessment
   - Clinical and adaptive scales
   - T-scores for behavioral indicators

5. **Conners** (ADHD Assessment)
   - ADHD-specific assessment
   - Attention and hyperactivity measures

---

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200`: Success
- `201`: Created (for POST requests)
- `400`: Bad Request (missing/invalid parameters)
- `404`: Not Found (resource doesn't exist)
- `500`: Internal Server Error

Error responses include details:
```json
{
  "error": "Patient not found"
}
```

---

## File Requirements

- **Supported formats**: PDF, DOC, DOCX
- **File size**: No explicit limit (reasonable sizes recommended)
- **Content**: Must be psychological assessment documents
- **Quality**: Text must be extractable (not scanned images without OCR)

---

## Getting Started

1. **Start the API server**:
   ```bash
   python3 psychometrist_api.py
   ```

2. **Test the health endpoint**:
   ```bash
   curl http://localhost:5000/api/health
   ```

3. **Create a patient**:
   ```bash
   curl -X POST http://localhost:5000/api/patients \
     -H "Content-Type: application/json" \
     -d '{"name": "Test Patient", "age": "10", "grade": "4"}'
   ```

4. **Upload assessments and generate reports** using the patient ID from step 3.

---

## Production Considerations

For production deployment, consider:

- **Authentication**: Add JWT tokens or API keys
- **Database**: Replace in-memory storage with PostgreSQL/MongoDB
- **File Storage**: Use cloud storage (AWS S3, Google Cloud Storage)
- **Rate Limiting**: Implement request rate limiting
- **HTTPS**: Use SSL certificates
- **HIPAA Compliance**: Ensure all data handling meets healthcare requirements
- **Logging**: Add comprehensive logging and monitoring
- **Backup**: Implement data backup strategies
