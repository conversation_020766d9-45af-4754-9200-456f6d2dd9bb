#!/usr/bin/env python3
"""
Test the Psychometrist Portal API
Step 8.1: Test all API endpoints
"""

import requests
import json
import os
import time

# API base URL
BASE_URL = "http://localhost:5000/api"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing Health Check...")
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['status']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the server is running.")
        return False

def test_create_patient():
    """Test creating a new patient"""
    print("\n👤 Testing Patient Creation...")
    
    patient_data = {
        "name": "Viktor Good",
        "age": "9:1",
        "grade": "3",
        "date_of_birth": "2015/01/25"
    }
    
    response = requests.post(f"{BASE_URL}/patients", json=patient_data)
    
    if response.status_code == 201:
        data = response.json()
        patient_id = data['patient']['id']
        print(f"✅ Patient created successfully: {patient_id}")
        return patient_id
    else:
        print(f"❌ Failed to create patient: {response.status_code}")
        print(response.text)
        return None

def test_upload_documents(patient_id):
    """Test uploading assessment documents"""
    print("\n📄 Testing Document Upload...")
    
    # Test files from our training data
    test_files = [
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/2_ViGo_WISC.pdf",
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/3_ViGo_WIAT_.pdf"
    ]
    
    assessment_ids = []
    
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"📤 Uploading {os.path.basename(file_path)}...")
            
            with open(file_path, 'rb') as f:
                files = {'files': f}
                data = {'patient_id': patient_id}
                
                response = requests.post(f"{BASE_URL}/upload", files=files, data=data)
                
                if response.status_code == 200:
                    result = response.json()
                    if result['results'] and result['results'][0]['status'] == 'success':
                        assessment_id = result['results'][0]['assessment_id']
                        doc_type = result['results'][0]['document_type']
                        confidence = result['results'][0]['confidence']
                        fields = result['results'][0]['fields_extracted']
                        
                        print(f"✅ Upload successful: {doc_type} (confidence: {confidence}, fields: {fields})")
                        assessment_ids.append(assessment_id)
                    else:
                        print(f"❌ Upload failed: {result['results'][0].get('error', 'Unknown error')}")
                else:
                    print(f"❌ Upload request failed: {response.status_code}")
        else:
            print(f"❌ File not found: {file_path}")
    
    return assessment_ids

def test_generate_report(patient_id, assessment_ids):
    """Test generating a comprehensive report"""
    print("\n📝 Testing Report Generation...")
    
    report_data = {
        "patient_id": patient_id,
        "assessment_ids": assessment_ids
    }
    
    response = requests.post(f"{BASE_URL}/reports/generate", json=report_data)
    
    if response.status_code == 200:
        data = response.json()
        report_id = data['report_id']
        print(f"✅ Report generated successfully: {report_id}")
        print(f"📄 Preview: {data['preview'][:200]}...")
        return report_id
    else:
        print(f"❌ Failed to generate report: {response.status_code}")
        print(response.text)
        return None

def test_get_patient(patient_id):
    """Test getting patient details"""
    print(f"\n👤 Testing Get Patient Details...")
    
    response = requests.get(f"{BASE_URL}/patients/{patient_id}")
    
    if response.status_code == 200:
        data = response.json()
        patient = data['patient']
        assessments = data['assessments']
        
        print(f"✅ Patient details retrieved:")
        print(f"   Name: {patient['name']}")
        print(f"   Age: {patient['age']}")
        print(f"   Assessments: {len(assessments)}")
        
        for assessment in assessments:
            print(f"   - {assessment['document_type']} (confidence: {assessment['classification_confidence']})")
        
        return True
    else:
        print(f"❌ Failed to get patient: {response.status_code}")
        return False

def test_get_statistics():
    """Test getting system statistics"""
    print(f"\n📊 Testing System Statistics...")
    
    response = requests.get(f"{BASE_URL}/stats")
    
    if response.status_code == 200:
        data = response.json()
        stats = data['statistics']
        
        print(f"✅ Statistics retrieved:")
        print(f"   Total Patients: {stats['total_patients']}")
        print(f"   Total Assessments: {stats['total_assessments']}")
        print(f"   Total Reports: {stats['total_reports']}")
        print(f"   Assessment Types: {stats['assessment_types']}")
        
        return True
    else:
        print(f"❌ Failed to get statistics: {response.status_code}")
        return False

def test_classify_document():
    """Test document classification endpoint"""
    print(f"\n🔍 Testing Document Classification...")
    
    test_file = "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/11_ViGo_BASC_Parent.pdf"
    
    if os.path.exists(test_file):
        with open(test_file, 'rb') as f:
            files = {'file': f}
            
            response = requests.post(f"{BASE_URL}/classify", files=files)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Classification successful:")
                print(f"   Document Type: {data['classification']}")
                print(f"   Confidence: {data['confidence']}")
                print(f"   Text Length: {data['text_length']}")
                return True
            else:
                print(f"❌ Classification failed: {response.status_code}")
                return False
    else:
        print(f"❌ Test file not found: {test_file}")
        return False

def run_full_api_test():
    """Run complete API test suite"""
    print("🚀 Starting Psychometrist Portal API Test Suite")
    print("=" * 60)
    
    # Test 1: Health Check
    if not test_health_check():
        print("❌ API server is not running. Please start it first with:")
        print("   python3 psychometrist_api.py")
        return
    
    # Test 2: Create Patient
    patient_id = test_create_patient()
    if not patient_id:
        print("❌ Cannot continue without patient ID")
        return
    
    # Test 3: Upload Documents
    assessment_ids = test_upload_documents(patient_id)
    if not assessment_ids:
        print("❌ No assessments uploaded successfully")
        return
    
    # Test 4: Generate Report
    report_id = test_generate_report(patient_id, assessment_ids)
    
    # Test 5: Get Patient Details
    test_get_patient(patient_id)
    
    # Test 6: Get Statistics
    test_get_statistics()
    
    # Test 7: Document Classification
    test_classify_document()
    
    print("\n" + "=" * 60)
    print("🎯 API TEST SUITE COMPLETE!")
    print("=" * 60)
    
    if report_id:
        print(f"✅ All tests passed!")
        print(f"📝 Generated report ID: {report_id}")
        print(f"👤 Created patient ID: {patient_id}")
        print(f"📄 Uploaded {len(assessment_ids)} assessments")
        print("\n🌐 API is ready for web interface integration!")
    else:
        print("⚠️  Some tests failed, but core functionality is working")

if __name__ == "__main__":
    # Wait a moment for server to start if running together
    print("⏳ Waiting for API server to start...")
    time.sleep(2)
    
    run_full_api_test()
