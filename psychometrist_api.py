#!/usr/bin/env python3
"""
Psychometrist Portal API
Step 8: Build REST API endpoints for the web interface
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
import json
import uuid
from datetime import datetime
import tempfile
from werkzeug.utils import secure_filename

# Import our AI components
from multi_assessment_extractor import MultiAssessmentExtractor
from comprehensive_report_generator import ComprehensiveReportGenerator
from document_classifier import DocumentClassifier

app = Flask(__name__)
CORS(app)  # Enable CORS for web interface

# Configuration
UPLOAD_FOLDER = 'uploads'
REPORTS_FOLDER = 'generated_reports'
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx'}

# Ensure directories exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(REPORTS_FOLDER, exist_ok=True)

# Initialize AI components
extractor = MultiAssessmentExtractor()
report_generator = ComprehensiveReportGenerator()
classifier = DocumentClassifier()

# In-memory storage for demo (in production, use a database)
patients = {}
assessments = {}
reports = {}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/patients', methods=['GET', 'POST'])
def handle_patients():
    """Get all patients or create a new patient"""
    
    if request.method == 'GET':
        return jsonify({
            'patients': list(patients.values()),
            'count': len(patients)
        })
    
    elif request.method == 'POST':
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'age', 'grade']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Create new patient
        patient_id = str(uuid.uuid4())
        patient = {
            'id': patient_id,
            'name': data['name'],
            'age': data['age'],
            'grade': data.get('grade'),
            'date_of_birth': data.get('date_of_birth'),
            'created_at': datetime.now().isoformat(),
            'assessments': []
        }
        
        patients[patient_id] = patient
        
        return jsonify({
            'message': 'Patient created successfully',
            'patient': patient
        }), 201

@app.route('/api/patients/<patient_id>', methods=['GET', 'PUT', 'DELETE'])
def handle_patient(patient_id):
    """Get, update, or delete a specific patient"""
    
    if patient_id not in patients:
        return jsonify({'error': 'Patient not found'}), 404
    
    if request.method == 'GET':
        patient = patients[patient_id]
        # Include assessment details
        patient_assessments = []
        for assessment_id in patient['assessments']:
            if assessment_id in assessments:
                patient_assessments.append(assessments[assessment_id])
        
        return jsonify({
            'patient': patient,
            'assessments': patient_assessments
        })
    
    elif request.method == 'PUT':
        data = request.get_json()
        patient = patients[patient_id]
        
        # Update patient fields
        updatable_fields = ['name', 'age', 'grade', 'date_of_birth']
        for field in updatable_fields:
            if field in data:
                patient[field] = data[field]
        
        patient['updated_at'] = datetime.now().isoformat()
        
        return jsonify({
            'message': 'Patient updated successfully',
            'patient': patient
        })
    
    elif request.method == 'DELETE':
        del patients[patient_id]
        return jsonify({'message': 'Patient deleted successfully'})

@app.route('/api/upload', methods=['POST'])
def upload_documents():
    """Upload and process assessment documents"""
    
    if 'files' not in request.files:
        return jsonify({'error': 'No files provided'}), 400
    
    patient_id = request.form.get('patient_id')
    if not patient_id or patient_id not in patients:
        return jsonify({'error': 'Valid patient_id required'}), 400
    
    files = request.files.getlist('files')
    results = []
    
    for file in files:
        if file.filename == '':
            continue
        
        if file and allowed_file(file.filename):
            # Save uploaded file
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            unique_filename = f"{timestamp}_{filename}"
            file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
            file.save(file_path)
            
            try:
                # Process the document
                extracted_data = extractor.extract_from_file(file_path)
                
                if 'error' in extracted_data:
                    results.append({
                        'filename': filename,
                        'status': 'error',
                        'error': extracted_data['error']
                    })
                    continue
                
                # Create assessment record
                assessment_id = str(uuid.uuid4())
                assessment = {
                    'id': assessment_id,
                    'patient_id': patient_id,
                    'filename': filename,
                    'file_path': file_path,
                    'document_type': extracted_data['document_type'],
                    'classification_confidence': extracted_data['classification_confidence'],
                    'extracted_data': extracted_data,
                    'uploaded_at': datetime.now().isoformat(),
                    'status': 'processed'
                }
                
                assessments[assessment_id] = assessment
                patients[patient_id]['assessments'].append(assessment_id)
                
                results.append({
                    'filename': filename,
                    'status': 'success',
                    'assessment_id': assessment_id,
                    'document_type': extracted_data['document_type'],
                    'confidence': extracted_data['classification_confidence'],
                    'fields_extracted': sum(len(v) if isinstance(v, dict) else 0 
                                          for k, v in extracted_data.items() 
                                          if k not in ['document_type', 'classification_confidence', 'text_length', 'file_path', 'file_name'])
                })
                
            except Exception as e:
                results.append({
                    'filename': filename,
                    'status': 'error',
                    'error': str(e)
                })
        else:
            results.append({
                'filename': file.filename,
                'status': 'error',
                'error': 'File type not allowed'
            })
    
    return jsonify({
        'message': f'Processed {len(results)} files',
        'results': results
    })

@app.route('/api/assessments/<assessment_id>', methods=['GET'])
def get_assessment(assessment_id):
    """Get detailed assessment data"""
    
    if assessment_id not in assessments:
        return jsonify({'error': 'Assessment not found'}), 404
    
    assessment = assessments[assessment_id]
    return jsonify({'assessment': assessment})

@app.route('/api/classify', methods=['POST'])
def classify_document():
    """Classify a document without full processing"""
    
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    
    if file.filename == '' or not allowed_file(file.filename):
        return jsonify({'error': 'Invalid file'}), 400
    
    # Save temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
        file.save(temp_file.name)
        
        try:
            # Extract text and classify
            from pdf_extractor import extract_text_from_pdf
            text = extract_text_from_pdf(temp_file.name)
            
            if text.startswith('ERROR'):
                return jsonify({'error': text}), 400
            
            classification = classifier.classify_document(text)
            
            return jsonify({
                'classification': classification['classification'],
                'confidence': classification['confidence'],
                'all_scores': classification['all_scores'],
                'text_length': len(text)
            })
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
        
        finally:
            # Clean up temp file
            os.unlink(temp_file.name)

@app.route('/api/reports/generate', methods=['POST'])
def generate_report():
    """Generate a comprehensive report for a patient"""
    
    data = request.get_json()
    patient_id = data.get('patient_id')
    assessment_ids = data.get('assessment_ids', [])
    
    if not patient_id or patient_id not in patients:
        return jsonify({'error': 'Valid patient_id required'}), 400
    
    # Get assessment file paths
    file_paths = []
    assessment_types = []
    
    for assessment_id in assessment_ids:
        if assessment_id in assessments:
            assessment = assessments[assessment_id]
            file_paths.append(assessment['file_path'])
            assessment_types.append(assessment['document_type'])
    
    if not file_paths:
        return jsonify({'error': 'No valid assessments found'}), 400
    
    try:
        # Generate comprehensive report
        report_content = report_generator.generate_comprehensive_report(file_paths)
        
        # Save report
        report_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_filename = f"report_{patient_id}_{timestamp}.txt"
        report_path = os.path.join(REPORTS_FOLDER, report_filename)
        
        with open(report_path, 'w') as f:
            f.write(report_content)
        
        # Create report record
        report_record = {
            'id': report_id,
            'patient_id': patient_id,
            'assessment_ids': assessment_ids,
            'assessment_types': assessment_types,
            'filename': report_filename,
            'file_path': report_path,
            'generated_at': datetime.now().isoformat(),
            'status': 'completed'
        }
        
        reports[report_id] = report_record
        
        return jsonify({
            'message': 'Report generated successfully',
            'report_id': report_id,
            'report': report_record,
            'preview': report_content[:500] + '...' if len(report_content) > 500 else report_content
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/reports/<report_id>', methods=['GET'])
def get_report(report_id):
    """Get report details or download report file"""
    
    if report_id not in reports:
        return jsonify({'error': 'Report not found'}), 404
    
    report = reports[report_id]
    
    # Check if requesting file download
    if request.args.get('download') == 'true':
        if os.path.exists(report['file_path']):
            return send_file(
                report['file_path'],
                as_attachment=True,
                download_name=report['filename'],
                mimetype='text/plain'
            )
        else:
            return jsonify({'error': 'Report file not found'}), 404
    
    # Return report metadata
    return jsonify({'report': report})

@app.route('/api/reports', methods=['GET'])
def list_reports():
    """List all reports, optionally filtered by patient"""
    
    patient_id = request.args.get('patient_id')
    
    if patient_id:
        patient_reports = [r for r in reports.values() if r['patient_id'] == patient_id]
        return jsonify({
            'reports': patient_reports,
            'count': len(patient_reports)
        })
    
    return jsonify({
        'reports': list(reports.values()),
        'count': len(reports)
    })

@app.route('/api/stats', methods=['GET'])
def get_statistics():
    """Get system statistics"""
    
    # Calculate statistics
    total_patients = len(patients)
    total_assessments = len(assessments)
    total_reports = len(reports)
    
    # Assessment type breakdown
    assessment_types = {}
    for assessment in assessments.values():
        doc_type = assessment['document_type']
        assessment_types[doc_type] = assessment_types.get(doc_type, 0) + 1
    
    # Recent activity
    recent_assessments = sorted(
        assessments.values(),
        key=lambda x: x['uploaded_at'],
        reverse=True
    )[:5]
    
    recent_reports = sorted(
        reports.values(),
        key=lambda x: x['generated_at'],
        reverse=True
    )[:5]
    
    return jsonify({
        'statistics': {
            'total_patients': total_patients,
            'total_assessments': total_assessments,
            'total_reports': total_reports,
            'assessment_types': assessment_types
        },
        'recent_activity': {
            'assessments': recent_assessments,
            'reports': recent_reports
        }
    })

if __name__ == '__main__':
    print("🚀 Starting Psychometrist Portal API")
    print("=" * 50)
    print("API Endpoints:")
    print("• GET  /api/health - Health check")
    print("• GET  /api/patients - List patients")
    print("• POST /api/patients - Create patient")
    print("• GET  /api/patients/<id> - Get patient details")
    print("• POST /api/upload - Upload assessment documents")
    print("• POST /api/classify - Classify document")
    print("• POST /api/reports/generate - Generate comprehensive report")
    print("• GET  /api/reports/<id> - Get/download report")
    print("• GET  /api/stats - System statistics")
    print("=" * 50)
    print("Starting server on http://localhost:8000")
    
    app.run(debug=True, host='0.0.0.0', port=8000)
